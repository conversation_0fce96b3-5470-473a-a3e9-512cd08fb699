<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<manifest xmlns="http://tizen.org/ns/packages" api-version="2.3.2" package="org.example.rawsensordata" version="1.0.0">
    <profile name="wearable"/>
    <service-application appid="org.example.rawsensordata" auto-restart="true" exec="rawsensordata" multiple="false" nodisplay="true" on-boot="true" taskmanage="false" type="capp">
        <label>rawsensordata</label>
        <icon>rawsensordata.png</icon>
        <metadata key="http://tizen.org/metadata/background-category/background-network"/>
        <metadata key="http://tizen.org/metadata/background-category/sensor"/>
        <metadata key="http://tizen.org/metadata/background-category/location"/>
        <metadata key="accessory-services-location" value="/res/xml/accessoryservices.xml"/>
    </service-application>
    <privileges>
        <privilege>http://tizen.org/privilege/network.get</privilege>
        <privilege>http://tizen.org/privilege/healthinfo</privilege>
        <privilege>http://tizen.org/privilege/network.set</privilege>
        <privilege>http://tizen.org/privilege/internet</privilege>
        <privilege>http://developer.samsung.com/tizen/privilege/accessoryprotocol</privilege>
        <privilege>http://tizen.org/privilege/network.profile</privilege>
        <privilege>http://tizen.org/privilege/power</privilege>
    </privileges>
</manifest>
