@echo off
echo ========================================
echo    Tizen Project Build and Run Script
echo ========================================

echo.
echo [1/5] Checking device connection...
C:\tizen-studio\tools\sdb.exe devices
if %errorlevel% neq 0 (
    echo ERROR: No device connected!
    pause
    exit /b 1
)

echo.
echo [2/5] Cleaning previous build...
tizen clean

echo.
echo [3/5] Building project...
tizen build-native -a arm -r wearable-4.0-device.core -C Debug
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo [4/5] Packaging application...
tizen package -t tpk -s Samsung -- Debug
if %errorlevel% neq 0 (
    echo ERROR: Packaging failed!
    pause
    exit /b 1
)

echo.
echo [5/5] Installing and launching on device...
C:\tizen-studio\tools\sdb.exe -s ***********:26101 install Debug\org.example.rawsensordata-1.0.0-arm.tpk
if %errorlevel% neq 0 (
    echo ERROR: Installation failed!
    pause
    exit /b 1
)

echo.
echo Launching application...
C:\tizen-studio\tools\sdb.exe -s ***********:26101 shell app_launcher -s org.example.rawsensordata

echo.
echo ========================================
echo    Application launched successfully!
echo ========================================
echo.
echo To view logs, run:
echo C:\tizen-studio\tools\sdb.exe -s ***********:26101 dlog X_rawsensordata
echo.
pause
